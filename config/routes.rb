Rails.application.routes.draw do
  devise_for :users
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # User dashboard
  get 'dashboard', to: 'dashboard#index', as: :user_dashboard

  # User accounts
  resources :accounts do
    member do
      get :get_ctrader_token
    end
  end

  # cTrader OAuth callback
  get 'ctraderauth', to: 'accounts#ctrader_callback'

  # User bots (scripts)
  resources :bots do
    member do
      post :start
      post :stop
      get :status
      get :logs
    end
  end

  # Admin routes
  get 'admin/dashboard', to: 'admin#dashboard'

  # Docker container routes
  resources :docker_containers, only: [:index, :show, :destroy] do
    member do
      post :start
      post :stop
      post :restart
    end

    collection do
      post :clone_celery_worker
      post :init_base_services
      post :destroy_all_containers
      post :add_mt5_server
    end
  end

  # Internal API routes for container communication
  namespace :api do
    post 'mt5_server_search', to: 'internal#mt5_server_search'
    post 'mt5_escape_window', to: 'internal#mt5_escape_window'
    post 'script_notification', to: 'internal#script_notification'
    post 'bot_restart', to: 'internal#bot_restart'
  end

  # Defines the root path route ("/")
  root "pages#index"
end
