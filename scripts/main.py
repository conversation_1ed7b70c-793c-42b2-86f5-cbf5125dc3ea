#!/usr/bin/env python
# coding: utf-8

import os, sys
import logging
import signal
import traceback

# Get log level from environment variable or default to INFO
log_level_name = os.environ.get('LOG_LEVEL', 'INFO')
log_level = getattr(logging, log_level_name.upper(), logging.DEBUG)

# Create logs directory if it doesn't exist
os.makedirs('./logs', exist_ok=True)

# Configure logging to both console and file
logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # Console handler
        logging.FileHandler(f'./logs/script_{ sys.argv[1] if len(sys.argv) > 1 else "main"}.log')  # File handler
    ]
)
logger = logging.getLogger(__name__)
logger.info(f"Logging initialized with level: {log_level_name}")

# Set up global exception handler to catch and log unhandled exceptions
def global_exception_handler(exctype, value, tb):
    """Global exception handler to log unhandled exceptions before crashing"""
    logger.critical(
        "Unhandled exception: %s", value,
        exc_info=(exctype, value, tb)
    )
    # Print to stderr as well (original behavior)
    sys.__excepthook__(exctype, value, tb)

# Install the global exception handler
sys.excepthook = global_exception_handler

# Function to log the current stack trace - useful for debugging
def log_stack_trace(level=logging.DEBUG):
    """Log the current stack trace at the specified level"""
    stack_trace = ''.join(traceback.format_stack())
    logger.log(level, "Current stack trace:\n%s", stack_trace)

# Set up signal handlers for graceful shutdown
def signal_handler(sig, frame):
    """Handle signals like SIGINT (Ctrl+C) and SIGTERM (kill command)"""
    signal_name = signal.Signals(sig).name if hasattr(signal, 'Signals') else f"signal {sig}"
    logger.info(f"Received {signal_name}, shutting down gracefully")

    # Log the current stack trace to help debug where the signal was received
    log_stack_trace(logging.INFO)

    # Stop the Twisted reactor from a thread-safe context
    if reactor.running:
        reactor.callFromThread(reactor.stop)
    else:
        logger.info("Reactor not running, exiting directly")
        reactor.stop()
        sys.exit(0)

# Register signal handlers
try:
    signal.signal(signal.SIGINT, signal_handler)   # Handle Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # Handle kill command
    logger.info("Signal handlers registered for graceful shutdown")
except Exception as e:
    logger.warning(f"Could not register signal handlers: {e}")

# Initialize config with environment variables and .env files
config = {
    **os.environ,  # override loaded values with environment variables
}

# Check if script_id is provided as a command-line argument
if len(sys.argv) > 1:
    try:
        script_id = int(sys.argv[1])
        logger.info(f"Running with script ID: {script_id}")
        # Set SCRIPT_ID in config for later use
        config['SCRIPT_ID'] = str(script_id)
    except ValueError:
        logger.error(f"Invalid script ID: {sys.argv[1]}")
        sys.exit(1)
else:
    logger.warning("No script ID provided, using default configuration")
from ctrader_open_api import Client, Protobuf, TcpProtocol, Auth, EndPoints
from ctrader_open_api.messages.OpenApiCommonMessages_pb2 import *
from ctrader_open_api.messages.OpenApiMessages_pb2 import *
from ctrader_open_api.messages.OpenApiModelMessages_pb2 import *
from twisted.internet import reactor, defer, task
from twisted.python import log as twisted_log
from twisted.python.failure import Failure
import json, pickle, pytz

# Set up Twisted error observer to log Twisted errors
def twisted_error_observer(event):
    """Log Twisted errors through our logging system"""
    if 'failure' in event and isinstance(event['failure'], Failure):
        logger.error("Twisted error: %s", event['failure'].getErrorMessage())
        logger.error("Traceback: %s", event['failure'].getTraceback())
    elif 'message' in event:
        logger.debug("Twisted log: %s", event['message'])

# Install the Twisted error observer
twisted_log.addObserver(twisted_error_observer)
from db_driver import *
# from datetime import datetime, timedelta
from datetime import datetime

def show_current_server_time():
    # Define the UTC+7 time zone
    timezone = pytz.timezone('Asia/Bangkok')  # Adjust this if needed for your specific UTC+7 location
    current_time = datetime.now(timezone)
    logger.info("Current server time (UTC+7): %s", current_time.strftime('%Y-%m-%d %H:%M:%S'))

try:
    db_fail_check = 0
    loop_health_db_check_deffered = None
    symbols = None
    connection_pool = ConnectionPool(connection_string=config['CONNECTION_STRING'])

    # Load script config from database if SCRIPT_ID is provided
    script_id = config.get('SCRIPT_ID')
    if script_id:
        db_config = connection_pool.load_config(script_id)
        if db_config:
            logger.info("Loaded config from database for script ID: %s", script_id)
            # Override config with values from database
            config.update(db_config)
            logger.debug("Updated config: %s", config)
        else:
            logger.warning("No config found in database for script ID: %s", script_id)


    # Get account info from database
    df_account = connection_pool.get_ctrader_account_info(config['FROM_ACCOUNT_ID'])
    if df_account.empty:
        logger.error("Cannot find account info for this account: %s", config['FROM_ACCOUNT_ID'])
        # Safely stop the reactor first
        if reactor.running:
            reactor.callFromThread(reactor.stop)
        else:
            reactor.stop()
            sys.exit(1)
    config['ACCOUNT_ID'] = df_account.account_id[0]
    config['ACCESS_TOKEN'] = df_account.access_token[0]
    config['REFRESH_TOKEN'] = df_account.refresh_token[0]
    config['HOST_TYPE'] = df_account.host_type[0]

    account_id = int(config['ACCOUNT_ID'])
    host = EndPoints.PROTOBUF_LIVE_HOST if config['HOST_TYPE'].lower() == "live" else EndPoints.PROTOBUF_DEMO_HOST
    client = Client(host, EndPoints.PROTOBUF_PORT, TcpProtocol)

    MAX_RETRIES = 3
    retry_count = 0
    DELAY_REFRESH_TOKEN = 2222000

    from mt5linux import MetaTrader5
    from tasks import mt5_order_send, delay_set_tp_sl
    mt5 = MetaTrader5(host=config['MT5_URL'], port=8001)
except Exception as e:
    logger.error("Error initializing main components: %s", str(e), exc_info=True)
    # Safely stop the reactor if it's running
    if 'reactor' in globals() and reactor.running:
        reactor.callFromThread(reactor.stop)
    else:
        sys.exit(1)

# Connect to MetaTrader 5 terminal
# while True:
#     try:
#         mt5 = MetaTrader5(host=config['MT5_URL'], port=8001)
#         if mt5.initialize(login=int(config['MT5_LOGIN']), server=config['MT5_SERVER'], password=config['MT5_PASSWORD']):
#             break
#         else:
#             print('Waiting for mt5 service')
#             time.sleep(2)
#     except Exception as e:
#         print('MT5 init fail: ', e)
#         print('Retrying...')
class SymbolCache:
    def __init__(self):
        self.cache = {}

    def get_symbol_data_sync(self, symbol):
        if symbol.symbolName in self.cache:
            d = defer.Deferred()
            d.callback(ProtoOASymbol.FromString(self.cache[symbol.symbolName]))
            return d
        else:
            return self.fetch_symbol_data_async(symbol)

    def fetch_symbol_callback(self, result_fetch, symbol):
        result_fetch = Protobuf.extract(result_fetch)
        symbol_data = result_fetch.symbol[0]
        self.cache[symbol.symbolName] = symbol_data.SerializeToString()
        # save_detail_symbols_cache.delay(pickle.dumps(self.cache))
        return symbol_data

    def clear_symbol_cache(self, symbol_ids):
        global symbols
        # Show server time
        show_current_server_time()
        # Just self restart script
        print('Get event update symbol from server -> clear cache symbol')
        for symbol_id in symbol_ids:
            symbol = find_symbol_by_id(symbols, symbol_id)
            if symbol.symbolName in self.cache:
                self.cache.pop(symbol.symbolName)

    def fetch_symbol_data_async(self, symbol):
        request = ProtoOASymbolByIdReq()
        request.ctidTraderAccountId = account_id
        request.symbolId.extend([symbol.symbolId])
        deferred = client.send(request)
        deferred.addCallback(lambda result: self.fetch_symbol_callback(result, symbol))
        deferred.addErrback(lambda failure: self.fetch_symbol_errback(failure, symbol))
        return deferred

    def fetch_symbol_errback(self, failure, symbol):
        # Handle the error here, e.g., log the error, retry, or return a default value
        print(f"Error fetching symbol data for {symbol.symbolName}: {failure.getErrorMessage()}")
        show_current_server_time()
        # Optionally, you can return a default value or re-raise the error
        return None

cache = SymbolCache()

host_info = {
    'account_id': config['ACCOUNT_ID'],
    'ota': 'cTrader'
}

def init_slave_account(account_id):
    return connection_pool.get_slave_accounts(account_id)

def refreshAccountToken():
    global account_id
    df_access_token = connection_pool.get_access_token(account_id)
    if df_access_token is None:
        logger.error('Cannot find old access token for this account: %s', account_id)
        # Safely stop the reactor first
        if reactor.running:
            reactor.callFromThread(reactor.stop)
        else:
            reactor.stop()
            sys.exit(1)
    request = ProtoOARefreshTokenReq()
    request.refreshToken = df_access_token.refresh_token[0]
    client.send(request)

def refreshAccountTokenDB(access_token):
    if access_token is None:
        logger.error('Refresh access token fail, try to stop the script')
        # Safely stop the reactor first
        if reactor.running:
            reactor.callFromThread(reactor.stop)
        else:
            reactor.stop()
            sys.exit(1)

    access_token = {
        'account_id': account_id,
        'access_token': access_token.accessToken,
        'refresh_token': access_token.refreshToken,
        'expires_in': access_token.expiresIn
    }
    # Update new access token
    connection_pool.refresh_access_token(access_token)
    # Restart syncbot script
    logger.info('Got new access token, restarting to apply')
    # Send request restart script



def find_symbol_by_id(symbols, target_id):
    if symbols is None:
        return None
    for symbol in symbols:
        if symbol.symbolId == target_id:
            return symbol

def setScheduleRefreshToken():
    print('Set refresh account token delay job')
    reactor.callLater(DELAY_REFRESH_TOKEN, refreshAccountToken)

def symbolsResponseCallback(result):
    logger.info("Symbols received")
    global symbols
    try:
        symbols = Protobuf.extract(result).symbol
    except Exception as e:
        logger.error("Get symbols data error, invalid access token or request: %s", str(e))
        return
    if config['AUTO_REFRESH_TOKEN'] == '1':
        setScheduleRefreshToken()

def accountAuthResponseCallback(result):
    logger.info("Account authenticated")
    request = ProtoOASymbolsListReq()
    request.ctidTraderAccountId = account_id
    request.includeArchivedSymbols = False
    deferred = client.send(request)
    deferred.addCallbacks(symbolsResponseCallback, onError)

def applicationAuthResponseCallback(result):
    logger.info("Application authenticated")
    logger.info("Account ID: %s", account_id)
    logger.info("Host Type: %s", config['HOST_TYPE'])

    # current_timezone = pytz.timezone(config['TZ'])
    # expired_time = df_access_token.updated_at[0].to_pydatetime().astimezone(current_timezone) + \
    # timedelta(seconds=int(df_access_token.expires_in[0]))
    request = ProtoOAAccountAuthReq()
    request.ctidTraderAccountId = account_id
    request.accessToken = config['ACCESS_TOKEN']
    deferred = client.send(request)
    deferred.addCallbacks(accountAuthResponseCallback, onError)

def onError(message): # Call back for errors
    logger.error("Message Error Callback: %s", message)

def disconnected(client, reason): # Callback for client disconnection
    global loop_health_db_check_deffered
    logger.warning("Disconnected Callback Run: %s", reason)
    show_current_server_time()

    global retry_count

    if retry_count < MAX_RETRIES:
        retry_count += 1
        logger.info("Attempting reconnect (%d/%d)...", retry_count, MAX_RETRIES)
        reactor.callLater(5, client.startService)  # Wait 5 seconds before reconnecting
    else:
        logger.critical("Max retries reached. Shutting down.")
        reactor.stop()

def onMessageReceived(client, message): # Callback for receiving all messages
    if message.payloadType in [ProtoHeartbeatEvent().payloadType, ProtoOAAccountAuthRes().payloadType,\
    ProtoOAApplicationAuthRes().payloadType, ProtoOASymbolsListRes().payloadType, ProtoOAGetTrendbarsRes().payloadType, ProtoOASymbolByIdRes().payloadType]:
        return

    extracted_message = Protobuf.extract(message)
    logger.debug("Message type: %s", message.payloadType)
    logger.debug("Message received: %s", extracted_message)

    global db_fail_check
    if db_fail_check > 0:
        e = connection_pool.check_health()
        if e != True:
            logger.warning("DB not in good health now, ignoring message event")
            return
        else:
            db_fail_check = 0

    if message.payloadType == ProtoOAErrorRes().payloadType:
        if extracted_message.errorCode == 'CH_ACCESS_TOKEN_INVALID':
            logger.error("Access token invalid")
            refreshAccountToken()

        if extracted_message.errorCode == 'OA_AUTH_TOKEN_EXPIRED':
            logger.warning("Auth token expired, refreshing token")
            refreshAccountToken()
        if extracted_message.errorCode == 'CANT_ROUTE_REQUEST':
            logger.error("Can't route request")
            reactor.stop()

    if message.payloadType == ProtoOASymbolChangedEvent().payloadType:
        cache.clear_symbol_cache(extracted_message.symbolId)
        return

    if message.payloadType == ProtoOAExecutionEvent().payloadType:
        if extracted_message.executionType == 3 and extracted_message.order.orderType == ProtoOAOrderType.STOP_LOSS_TAKE_PROFIT:
            logger.info('Update status order get TP or SL')
            connection_pool.update_status_host_deal(extracted_message.order.orderId, account_id, 'CtraderAccount', ConnectionPool.ORDER_STATUS['closed'])
            return
        executionEventHandler(extracted_message)
        return
    if message.payloadType == ProtoOARefreshTokenRes().payloadType:
        refreshAccountTokenDB(extracted_message)
        return

def reset_retries():
    global retry_count
    retry_count = 0

def connected(client): # Callback for client connection
    logger.info("Connected to server")
    global loop_health_db_check_deffered
    reset_retries()
    loop_health_db_check = task.LoopingCall(db_check_health)
    loop_health_db_check_deffered = loop_health_db_check.start(120)
    request = ProtoOAApplicationAuthReq()
    request.clientId = config['CLIENT_ID']
    request.clientSecret = config['CLIENT_SECRET']
    deferred = client.send(request)
    deferred.addCallbacks(applicationAuthResponseCallback, onError)

# Setting optional client callbacks
client.setConnectedCallback(connected)
client.setDisconnectedCallback(disconnected)
client.setMessageReceivedCallback(onMessageReceived)


def executionEventHandler(message):
    if message.ctidTraderAccountId != account_id:
        return
    type_need_preload_symbol_data = [2, 3, 4, 5, 6]
    global slave_accounts
    for _, slave_account in slave_accounts.iterrows():
        mt5_info = {
            'login': int(slave_account['login']),
            'password': slave_account['password'],
            'server': slave_account['server'],
            'volume_leverage': slave_account['volume_leverage'],
        }
        if message.executionType in type_need_preload_symbol_data:
            preloadSymbolData(message, mt5_info, execution_handlers.get(message.executionType))
        else:
            execution_handlers.get(message.executionType)(message, mt5_info)


def calculate_lot(volume_in_cent, symbol_data, volume_lerverage):
    return round(float(volume_in_cent)/float(symbol_data.lotSize)*float(volume_lerverage), 2)



def copyAcceptedOrder(symbol_data, message, mt5_info):
    global symbols
    symbol = find_symbol_by_id(symbols, message.order.tradeData.symbolId)
    if symbol is None:
        print('There is no symbol id' + str(message.order.tradeData.symbolId))
        return

    if  (message.order.orderType == 2 or message.order.orderType == 3) and message.position.positionStatus == ProtoOAPositionStatus.POSITION_STATUS_CREATED: #Limit order or stop order
        print('Make new copy limit order or stop order accept call')
        # Save open order
        order = {
            'order_id': message.order.orderId,
            'position_id': message.position.positionId,
            'accountable_type': 'CtraderAccount',
            'accountable_id': config['ACCOUNT_ID'],
            'status': ConnectionPool.ORDER_STATUS['open'],
            'trade_side': 1 if message.order.tradeData.tradeSide == ProtoOATradeSide.BUY else 0
        }
        inserted_df = connection_pool.get_host_deal_by_position(message.position.positionId, account_id, 'CtraderAccount')
        if inserted_df.empty:
            inserted_df = connection_pool.insert_host_deal(order)
        if inserted_df is None:
            print('Inserted df is None, return')
            return
        lot = calculate_lot(message.order.tradeData.volume, symbol_data, mt5_info['volume_leverage'])
        if message.order.orderType == 2:
            orderType = {
                ProtoOATradeSide.BUY: mt5.ORDER_TYPE_BUY_LIMIT,
                ProtoOATradeSide.SELL: mt5.ORDER_TYPE_SELL_LIMIT
            }
            price = message.order.limitPrice  # Limit order price
        else:
            orderType = {
                ProtoOATradeSide.BUY: mt5.ORDER_TYPE_BUY_STOP,
                ProtoOATradeSide.SELL: mt5.ORDER_TYPE_SELL_STOP
            }
            price = message.order.stopPrice

        sl = 0.0
        tp = 0.0
        if message.order.stopLoss != 0 or message.order.relativeStopLoss != 0:
            sl = message.order.stopLoss or (price - message.order.relativeStopLoss*10**-5 if message.order.tradeData.tradeSide == ProtoOATradeSide.BUY else price + message.order.relativeStopLoss*10**-5)
        if message.order.takeProfit != 0 or message.order.relativeTakeProfit != 0:
            tp = message.order.takeProfit or (price + message.order.relativeTakeProfit*10**-5 if message.order.tradeData.tradeSide == ProtoOATradeSide.BUY else price - message.order.relativeTakeProfit*10**-5)

        # Prepare order request
        request = {
            "action": mt5.TRADE_ACTION_PENDING,
            "type": orderType.get(message.order.tradeData.tradeSide),
            "symbol": symbol.symbolName,
            "volume": lot,
            "price": price,
            "sl": sl,
            "tp": tp,
            "magic": 2268,
            "type_filling": mt5.ORDER_FILLING_IOC,
            "type_time": mt5.ORDER_TIME_GTC
        }

        slave_order = {
            'accountable_type': 'Mt5Account',
            'accountable_id': mt5_info['login'],
            'host_deal_id': int(inserted_df['id'][0]),
            'order_id': None,
            'position_id': None,
            'pending_changes': None
        }
        host_info['position_id'] = message.position.positionId
        mt5_order_send.delay(mt5_info, host_info, request, symbol_data.SerializeToString(), symbol.symbolName, slave_order)
        print('MT5 job sent')

    if message.order.orderType == ProtoOAOrderType.STOP_LOSS_TAKE_PROFIT and message.position.positionStatus == ProtoOAPositionStatus.POSITION_STATUS_OPEN:
        delay_set_tp_sl.delay(message.order.positionId, message.position.takeProfit, message.position.stopLoss, host_info, mt5_info, symbol_data.SerializeToString(), symbol.symbolName)
        print('Delay set tp sl job sent')


def make_mt5_market_request(symbol, lot, buy, sell, sl, tp, id_position=None, magic=1111):

    """ OPEN A TRADE """
    if buy and id_position==None:
        request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": mt5.ORDER_TYPE_BUY,
        "sl": sl,
        "tp": tp,
        "deviation": 11,
        "magic": magic,
        "type_time": mt5.ORDER_TIME_GTC}

    if sell and id_position==None:
        request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": mt5.ORDER_TYPE_SELL,
        "sl": sl,
        "tp": tp,
        "deviation": 11,
        "magic": magic,
        "type_time": mt5.ORDER_TIME_GTC}


    """ CLOSE A TRADE """
    if buy and id_position!=None:
        request = {
        "position": id_position,
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": mt5.ORDER_TYPE_BUY,
        "deviation": 11,
        "magic": magic,
        "type_time": mt5.ORDER_TIME_GTC}


    if sell and id_position!=None:
        request = {
        "position": id_position,
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": mt5.ORDER_TYPE_SELL,
        "deviation": 11,
        "magic": magic,
        "type_time": mt5.ORDER_TIME_GTC}

    return request


def copyOrderCancelled(symbol_data, message, mt5_info):
    global symbols
    symbol = find_symbol_by_id(symbols, message.order.tradeData.symbolId)
    if message.order.orderType == 2 or message.order.orderType == 3: #Limit order or Stop order
        print('Cancel limit or stop order')
        # Update status open order
        connection_pool.update_status_host_deal(message.order.orderId, account_id, 'CtraderAccount', ConnectionPool.ORDER_STATUS['cancelled'])
        # Get slave deals df
        slave_orders = connection_pool.get_slave_deal_by_host_position_id(message.order.positionId, account_id, 'CtraderAccount', mt5_info['login'], 'Mt5Account')

        if slave_orders.empty:
            print("Not found any slave order match to cancel")
            return
        if int(slave_orders.status[0]) in (1, 4):
            print("Slave order already cancelled or closed")
            return

        # Prepare order request
        request = {
            "action": mt5.TRADE_ACTION_REMOVE,
            "order": int(slave_orders['order_id'][0]),
            "magic": 2268
        }

        slave_order = {
            'order_id': slave_orders['order_id'][0],
            'accountable_id': slave_orders['accountable_id'][0],
            'accountable_type': slave_orders['accountable_type'][0],
            'status': int(slave_orders['status'][0]),
            'host_deal_id': int(slave_orders['host_deal_id'][0])
        }
        host_info['position_id'] = message.position.positionId
        mt5_order_send.delay(mt5_info, host_info, request, symbol_data.SerializeToString(), symbol.symbolName, slave_order)
        print('MT5 job sent')


    # Remove SL TP open position
    if message.order.orderType == ProtoOAOrderType.STOP_LOSS_TAKE_PROFIT and message.position.positionStatus == ProtoOAPositionStatus.POSITION_STATUS_OPEN:

        print('Remove open position SL TP')

        slave_orders = connection_pool.get_slave_deal_by_host_position_id(message.order.positionId, account_id, 'CtraderAccount', mt5_info['login'], 'Mt5Account')
        if slave_orders.empty:
            print("Not found any slave order match to cancel SL TP")
            return

        # Prepare order request
        request = {
            "action": mt5.TRADE_ACTION_SLTP,
            "position": int(slave_orders['position_id'][0]),
            "magic": 2268,
            "sl": 0.0,
            "tp": 0.0
        }

        slave_order = {
            'order_id': slave_orders['order_id'][0],
            'accountable_id': slave_orders['accountable_id'][0],
            'accountable_type': slave_orders['accountable_type'][0],
            'status': int(slave_orders['status'][0]),
            'host_deal_id': int(slave_orders['host_deal_id'][0])
        }
        host_info['position_id'] = message.position.positionId
        mt5_order_send.delay(mt5_info, host_info, request, symbol_data.SerializeToString(), symbol.symbolName, slave_order)
        print('MT5 job sent')



def copyModifiedOrder(symbol_data, message, mt5_info):
    global symbols
    symbol = find_symbol_by_id(symbols, message.order.tradeData.symbolId)
    if message.order.orderType == 2 or message.order.orderType == 3:
        print("Order replaced call modified limit stop order")

        slave_orders = connection_pool.get_slave_deal_by_host_position_id(message.order.positionId, account_id, 'CtraderAccount', mt5_info['login'], 'Mt5Account')
        if slave_orders.empty:
            print("Not found any slave order match to modify order")
            return
        lot = calculate_lot(message.order.tradeData.volume, symbol_data, mt5_info['volume_leverage'])
        if message.order.orderType == 2:
            orderType = {
                ProtoOATradeSide.BUY: mt5.ORDER_TYPE_BUY_LIMIT,
                ProtoOATradeSide.SELL: mt5.ORDER_TYPE_SELL_LIMIT
            }
            price = message.order.limitPrice  # Limit order price
        else:
            orderType = {
                ProtoOATradeSide.BUY: mt5.ORDER_TYPE_BUY_STOP,
                ProtoOATradeSide.SELL: mt5.ORDER_TYPE_SELL_STOP
            }
            price = message.order.stopPrice

        sl = 0.0
        tp = 0.0
        if message.order.stopLoss != 0 or message.order.relativeStopLoss != 0:
            sl = message.order.stopLoss or (price - message.order.relativeStopLoss*10**-5 if message.order.tradeData.tradeSide == ProtoOATradeSide.BUY else price + message.order.relativeStopLoss*10**-5)
        if message.order.takeProfit != 0 or message.order.relativeTakeProfit != 0:
            tp = message.order.takeProfit or (price + message.order.relativeTakeProfit*10**-5 if message.order.tradeData.tradeSide == ProtoOATradeSide.BUY else price - message.order.relativeTakeProfit*10**-5)
        # Prepare order request
        request = {
            "action": mt5.TRADE_ACTION_MODIFY,
            "type": orderType.get(message.order.tradeData.tradeSide),
            "order": int(slave_orders['order_id'][0]),
            "symbol": symbol.symbolName,
            "volume": lot,
            "price": price,
            "sl": sl,
            "tp": tp,
            "magic": 2268,
            "type_filling": mt5.ORDER_FILLING_IOC,
            "type_time": mt5.ORDER_TIME_GTC
        }
        # Modified limit the order
        slave_order = {
            'order_id': slave_orders['order_id'][0],
            'accountable_id': slave_orders['accountable_id'][0],
            'open_order_id': int(slave_orders['open_order_id'][0]),
            'accountable_type': slave_orders['accountable_type'][0],
            'status': int(slave_orders['status'][0]),
        }
        host_info['position_id'] = message.position.positionId
        mt5_order_send.delay(mt5_info, host_info, request, symbol_data.SerializeToString(), symbol.symbolName, slave_order)
        print('MT5 job sent')


    if message.order.orderType == ProtoOAOrderType.STOP_LOSS_TAKE_PROFIT and message.position.positionStatus == ProtoOAPositionStatus.POSITION_STATUS_OPEN:

        print('Modify open position SL TP')

        slave_orders = connection_pool.get_slave_deal_by_host_position_id(message.order.positionId, account_id, 'CtraderAccount', mt5_info['login'], 'Mt5Account')

        if slave_orders.empty:
            print("Not found any slave order match to modify SL TP")
            return

        # Prepare order request
        request = {
            "action": mt5.TRADE_ACTION_SLTP,
            "position": int(slave_orders['position_id'][0]),
            "magic": 2268,
            "sl": message.position.stopLoss,
            "tp": message.position.takeProfit
        }

        slave_order = {
            'order_id': slave_orders['order_id'][0],
            'accountable_id': slave_orders['accountable_id'][0],
            'host_deal_id': int(slave_orders['host_deal_id'][0]),
            'accountable_type': slave_orders['accountable_type'][0],
            'status': int(slave_orders['status'][0]),
        }
        host_info['position_id'] = message.position.positionId
        mt5_order_send.delay(mt5_info, host_info, request, symbol_data.SerializeToString(), symbol.symbolName, slave_order)
        print('MT5 job sent')


def cancelCtraderOrderRequest(message):
    request = ProtoOACancelOrderReq()
    request.orderId = message.order.orderId
    request.ctidTraderAccountId = account_id
    client.send(request)


def dumbFunc(message, mt5_info):
    print('Dumb called')


def updateOrderFilled(symbol_data, message, mt5_info):
    # if message.order.orderType == ProtoOAOrderType.STOP_LOSS_TAKE_PROFIT:
    #     print('Update status order get TP or SL')
    #     connection_pool.update_status_open_order(message.order.orderId, account_id, 'cTrader', ConnectionPool.ORDER_STATUS['closed'])

    # Update TP SL for market order filled
    print('order filled callback')
    global symbols
    symbol = find_symbol_by_id(symbols, message.order.tradeData.symbolId)
    if message.order.orderType == 1: # Market order
        inserted_df = connection_pool.get_host_deal_by_position(message.position.positionId, account_id, 'CtraderAccount')
        if inserted_df.empty:
            print('Make new copy market order open')
            new_order = {
                'order_id': message.order.orderId,
                'position_id': message.position.positionId,
                'accountable_type': 'CtraderAccount',
                'accountable_id': config['ACCOUNT_ID'],
                'status': ConnectionPool.ORDER_STATUS['open'],
                'trade_side': 1 if message.order.tradeData.tradeSide == ProtoOATradeSide.BUY else 0
            }
            inserted_df = connection_pool.insert_host_deal(new_order)
            if inserted_df is None:
                print('Inserted df is None, connection DB fail')
                return
            slave_order = {
                        'accountable_type': 'Mt5Account',
                        'accountable_id': mt5_info['login'],
                        'host_deal_id': int(inserted_df['id'][0]),
                        'order_id': None,
                        'pending_changes': None
                    }
            position_id = None
        else:
            try:
                if not message.deal.closePositionDetail:
                    print('Skip update order filled')
                    return
            except Exception as e:
                print('Skip update order filled')
                return
            if inserted_df['trade_side'][0] == (1 if message.order.tradeData.tradeSide == ProtoOATradeSide.BUY else 0):
                print('Skip update order filled')
                return
            print('Make new copy market order closed')
            is_complete_closed = True if message.order.tradeData.volume == message.position.tradeData.volume else False
            slave_orders_df = connection_pool.get_slave_deal_by_host_position_id(message.position.positionId, config['ACCOUNT_ID'], 'CtraderAccount', mt5_info['login'], 'Mt5Account')
            if is_complete_closed:
                connection_pool.update_status_host_deal(inserted_df['order_id'][0], config['ACCOUNT_ID'], 'CtraderAccount', ConnectionPool.ORDER_STATUS['closed'])
            if slave_orders_df.empty:
                print("Can not find any slave order")
                return
            slave_order = {
                        'accountable_type': str(slave_orders_df['accountable_type'][0]),
                        'accountable_id': str(slave_orders_df['accountable_id'][0]),
                        'host_deal_id': int(slave_orders_df['host_deal_id'][0]),
                        'order_id': str(slave_orders_df['order_id'][0]),
                        'pending_changes': slave_orders_df['pending_changes'][0]
                    }
            position_id = int(slave_orders_df['position_id'][0])

        is_buy_side = True if message.order.tradeData.tradeSide == ProtoOATradeSide.BUY else False
        lot = calculate_lot(message.order.tradeData.volume, symbol_data, mt5_info['volume_leverage'])
        sl = message.order.stopLoss
        tp = message.order.takeProfit

        request = make_mt5_market_request(symbol.symbolName, lot, is_buy_side, is_buy_side == False, sl, tp, position_id)
        host_info['position_id'] = message.position.positionId
        symbol_data_dumps = symbol_data.SerializeToString()
        mt5_order_send.delay(mt5_info, host_info, request, symbol_data_dumps, symbol.symbolName, slave_order)
        print('MT5 job sent')


def preloadSymbolData(message, mt5_info, callback):
    global symbols
    symbol = find_symbol_by_id(symbols, message.order.tradeData.symbolId)
    deferred = cache.get_symbol_data_sync(symbol)
    deferred.addCallback(lambda symbol_data: callback(symbol_data, message, mt5_info))


execution_handlers = {
    2: copyAcceptedOrder, # ORDER_ACCEPTED
    3: updateOrderFilled, # ORDER_FILLED
    4: copyModifiedOrder, # ORDER_REPLACED
    5: copyOrderCancelled, # ORDER_CANCELLED
    6: copyOrderCancelled, # ORDER_EXPIRED
    7: dumbFunc, # ORDER_REJECTED
    8: dumbFunc, # ORDER_CANCEL_REJECTED
    9: dumbFunc, # SWAP
    10: dumbFunc, # DEPOSIT_WITHDRAW
    11: dumbFunc, # ORDER_PARTIAL_FILL
    12: dumbFunc, # BONUS_DEPOSIT_WITHDRAW
}

def db_check_health():
    global db_fail_check
    e = connection_pool.check_health()
    if e == True:
        db_fail_check = 0
        # if client.running != 1 or client.isConnected is not True:
        #     logger.info("Client not running or not connected, starting service")
        #     client.startService()
        # time.sleep(2)
        # if client.isConnected is not True:
        #     logger.error('Cannot connect to client Service')
        #     logger.error('Stopping reactor')
        #     reactor.stop()
    else:
        db_fail_check = db_fail_check + 1
        logger.warning('Database health check failed, will retry in 120s')

if __name__ == '__main__':
    try:
        # Initialize the application
        logger.info("Starting application with script ID: %s", config.get('SCRIPT_ID', 'None'))

        # Set up error handling for deferred callbacks
        def handle_deferred_error(failure):
            logger.error("Unhandled error in deferred: %s", failure.getErrorMessage())
            logger.error("Traceback: %s", failure.getTraceback())
            return failure

        # Install the default deferred error handler
        defer.setDebugging(True)

        # Initialize slave accounts with error handling
        try:
            logger.info("Initializing slave accounts for account ID: %s", config['ACCOUNT_ID'])
            slave_accounts = init_slave_account(config['ACCOUNT_ID'])
        except Exception as e:
            logger.error("Error initializing slave accounts: %s", str(e), exc_info=True)
            if reactor.running:
                reactor.callFromThread(reactor.stop)
            else:
                sys.exit(1)

        # Starting the client service with error handling
        try:
            logger.info("Starting client service")
            client.startService()
        except Exception as e:
            logger.critical("Failed to start client service: %s", str(e), exc_info=True)
            # Safely stop the reactor if it's running
            if reactor.running:
                reactor.callFromThread(reactor.stop)
            else:
                sys.exit(1)

        # Run Twisted reactor with error handling
        try:
            logger.info("Starting Twisted reactor")
            reactor.run()
        except Exception as e:
            logger.critical("Twisted reactor error: %s", str(e), exc_info=True)
            # No need to call sys.exit(1) here as we're already handling the exception
            # and the reactor is likely not running if we got here

    except Exception as e:
        logger.critical("Fatal error in main execution: %s", str(e), exc_info=True)
        # Safely stop the reactor if it's running
        if reactor.running:
            reactor.callFromThread(reactor.stop)
        else:
            sys.exit(1)
    finally:
        # Ensure we log when the application is shutting down
        logger.info("Application shutting down")

        # Clean up any resources if needed
        try:
            if 'connection_pool' in globals() and connection_pool is not None:
                logger.info("Closing database connections")
                # Add any cleanup code for database connections here
        except Exception as e:
            logger.error("Error during cleanup: %s", str(e))

