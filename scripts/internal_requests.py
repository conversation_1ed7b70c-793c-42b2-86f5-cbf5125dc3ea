import requests
import logging


def send_mt5_escape_signal(config):
    """
    Send escape signal to MT5 container via API request.
    
    Args:
        config (dict): Configuration dictionary containing API settings
    """
    # Get API base URL and auth key from environment variables
    api_base_url = config.get('API_BASE_URL', 'http://localhost:3001')
    auth_key = config.get('INTERNAL_API_AUTH_KEY', '')

    # Prepare the request
    url = f"{api_base_url}/api/mt5_escape_window"
    headers = {'X-Internal-Auth-Key': auth_key}
    data = {
        'container_name': config.get('MT5_CONTAINER_NAME', 'mt5'),
        'auth_key': auth_key
    }

    # Send the request
    response = requests.post(url, headers=headers, data=data, timeout=5)

    # Log the response
    if response.status_code == 200:
        logging.info("Successfully sent escape signal to MT5 container")
    else:
        logging.error(f"Failed to send escape signal to MT5 container: {response.status_code} - {response.text}")
